import os
import time
import threading
import subprocess
import signal
# 添加调用栈信息
import traceback
from uvicorn.workers import UvicornWorker

class GPUMonitoringWorker(UvicornWorker):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.should_exit = False
        self.gpu_threshold = 0.8  # 显存占用阈值，80%
        self.monitor_thread = None

    def get_gpu_memory_usage(self):
        """获取当前进程的显存占用率"""
        try:
            # 获取当前进程ID
            pid = os.getpid()

            # 使用nvidia-smi命令获取显存信息
            cmd = f"nvidia-smi --query-compute-apps=pid,used_memory --format=csv,noheader,nounits"
            output = subprocess.check_output(cmd.split()).decode()

            # 如果输出为空，说明当前没有进程在使用 GPU
            if not output.strip():
                self.log.warning("No GPU processes found (normal during startup)")
                return 0.0

            # 解析输出找到当前进程的显存使用
            for line in output.strip().split('\n'):
                process_pid, memory_used = map(int, line.strip().split(','))
                if process_pid == pid:
                    # 获取显卡总显存
                    total_memory_cmd = "nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits"
                    total_memory = int(subprocess.check_output(total_memory_cmd.split()).decode().strip())

                    # 计算显存占用率
                    return memory_used

            # 如果没找到当前进程，说明还没开始使用 GPU
            self.log.info("Current process not using GPU yet")
            return 0.0
        except Exception as e:
            self.log.error(f"Error getting GPU memory usage: {e}")
            return 0.0

    def monitor_gpu(self):
        """监控显存使用的线程函数"""
        while not self.should_exit:
            usage = self.get_gpu_memory_usage()
            if usage > 400:
                self.log.warning(f"GPU memory usage ({usage})MB exceeded 400MB.")
                # self.log.warning(f"GPU memory usage ({usage:.2%}) exceeded threshold ({self.gpu_threshold:.2%})")
                # 通知 worker 不再接收新请求
                self.alive = False
                # 通知worker关闭
                self.notify_replace()
                # 给自己发送SIGTERM信号
                os.kill(os.getpid(), signal.SIGTERM)
                break
            time.sleep(1)  # 每秒检查一次

    def init_process(self):
        """初始化worker时启动监控线程"""
        self.monitor_thread = threading.Thread(target=self.monitor_gpu)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        return super().init_process()

    def stop(self):
        """停止worker时清理监控线程"""
        self.should_exit = True
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        return super().stop()

    def notify_replace(self):
        """通知 Gunicorn 主进程该 worker 需要被替换"""
        self.log.info("Notifying master process to replace this worker")
        #self.log.info(f"Notification call stack:\n{traceback.format_stack()}")
        # 通知主进程该worker不健康
        self.cfg.worker_int(self)
