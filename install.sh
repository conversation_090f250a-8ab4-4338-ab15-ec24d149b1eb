#!/bin/bash
# OCR服务器依赖离线安装脚本

# 无颜色输出

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "linux"* ]]; then
        if [ -f /etc/debian_version ]; then
            echo "debian"
        elif [ -f /etc/redhat-release ]; then
            echo "rhel"
        elif [ -f /etc/SuSE-release ] || [ -f /etc/SUSE-brand ]; then
            echo "suse"
        elif grep -qE "(ID|ID_LIKE)=\"?suse\"?|SUSE|openSUSE" /etc/os-release 2>/dev/null; then
            echo "suse"
        else
            echo "linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    else
        echo "unknown"
    fi
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查Python环境
check_python() {
    if ! command_exists python3; then
        echo "❌ Python3未安装"
        exit 1
    fi
    
    if ! command_exists pip3; then
        echo "❌ pip3未安装"
        exit 1
    fi
}

# 安装Python依赖
install_python_deps() {
    if [ ! -d "lib" ]; then
        echo "❌ lib目录不存在"
        exit 1
    fi
    
    if [ ! -f "requirements.txt" ]; then
        echo "❌ requirements.txt不存在"
        exit 1
    fi
    
    echo "📦 安装Python依赖..."
    pip3 install --find-links lib --no-index -r requirements.txt
    
    if [ $? -eq 0 ]; then
        echo "✅ Python依赖安装成功"
    else
        echo "❌ Python依赖安装失败"
        exit 1
    fi
}

# 主函数
main() {
    echo "🚀 开始离线安装OCR服务器依赖..."

    check_python
    install_python_deps
    
    echo "🎉 安装完成！"
    echo "启动服务: ./start.sh"
}

main 