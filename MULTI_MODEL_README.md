# 多模型文本识别功能说明

## 概述

本项目现在采用**严格的YAML配置驱动**的多模型架构，所有模型配置都**必须**来源于YAML文件，不提供任何fallback机制。如果YAML配置文件不存在或配置不完整，系统将直接抛出错误。支持同时加载多个文本识别模型，并可以通过参数选择使用哪个模型进行识别。

## 功能特性

- ✅ **严格的YAML配置驱动**，所有配置来源于YAML文件，无fallback
- ✅ **配置验证**，缺少必要配置时直接抛出详细错误信息
- ✅ 统一的多模型架构，无需区分单模型和多模型模式
- ✅ 支持同时加载多个识别模型
- ✅ 可通过参数动态选择使用哪个模型
- ✅ 灵活的配置管理，支持自定义YAML配置文件
- ✅ 支持高级模型参数配置（预热、优化级别等）
- ✅ 提供模型信息查询接口

## 可用模型

目前预配置了以下识别模型：

| 模型ID | 模型名称 | 特点 | 适用场景 |
|--------|----------|------|----------|
| `ppocrv5_server` | PPOCRv5 服务器版 | 精度高，速度较慢 | 高精度要求的文档识别 |
| `ppocrv5_mobile` | PPOCRv5 轻量版 | 速度快，精度中等 | 实时处理、移动端应用 |
| `ppocrv4` | PPOCRv4 | 速度最快，精度较低 | 快速预览、低精度要求场景 |

## YAML配置文件

系统使用`models_config.yaml`文件来配置所有模型。配置文件结构如下：

```yaml
models:
  ppocrv5_server:
    model_name: "PPOCRv5 服务器版"
    model_path: "inference/models/ppocrv5/rec/rec_server.onnx"
    algorithm: "SVTR_LCNet"
    char_dict_path: "inference/models/ppocrv5/ppocrv5_dict.txt"
    image_shape: "3,48,320"
    batch_num: 6
    use_space_char: true
    max_text_length: 25
    description: "PPOCRv5 服务器版本，精度较高"
    warmup_enabled: true
    optimization_level: "accurate"
    custom_params:
      use_fp16: false

global_config:
  # 模型选择配置
  default_model_id: "ppocrv5_server"  # 默认模型

  # 系统配置
  drop_score: 0.5
  inference_engine: "openvino"
  use_gpu: false
  use_angle_cls: false
  use_ori_cls: false
```

## 使用方法

### 1. 使用默认YAML配置

```python
from inference.ocr_engine import OCREngine

# 使用默认的models_config.yaml配置文件
ocr_engine = OCREngine(
    use_gpu=False,
    inference_engine='openvino'
    # 所有模型配置都来自YAML文件
)
```

### 2. 修改YAML配置来选择模型

```yaml
global_config:
  default_model_id: "ppocrv5_mobile"
  # 其他配置...
```

然后使用相同的代码：

```python
ocr_engine = OCREngine(
    use_gpu=False,
    inference_engine='openvino'
    # 模型选择完全由YAML配置决定
)
```

### 3. 使用自定义YAML配置文件

```python
# 使用自定义的YAML配置文件
ocr_engine = OCREngine(
    use_gpu=False,
    inference_engine='openvino',
    models_config_file='my_custom_config.yaml'
)
```

### 4. 查看可用模型

```python
# 获取可用模型列表
available_models = ocr_engine.get_available_rec_models()
print("可用模型:", available_models)

# 获取模型详细信息
for model_id in available_models:
    info = ocr_engine.get_rec_model_info(model_id)
    print(f"{model_id}: {info['model_name']} - {info['description']}")
```

### 5. 使用指定模型进行识别

```python
import cv2

# 加载图像
img = cv2.imread('your_image.jpg')

# 使用默认模型
result = ocr_engine.ocr(img)

# 使用指定模型
result = ocr_engine.ocr(img, rec_model_id='ppocrv5_mobile')
```

## 高级用法

### 自适应模型选择

```python
def select_model_by_image_size(img_shape):
    """根据图像大小选择合适的模型"""
    height, width = img_shape[:2]
    total_pixels = height * width
    
    if total_pixels > 1000000:  # 大图像
        return 'ppocrv5_server'  # 使用高精度模型
    else:  # 小图像
        return 'ppocrv5_mobile'  # 使用快速模型

# 使用
selected_model = select_model_by_image_size(img.shape)
result = ocr_engine.ocr(img, rec_model_id=selected_model)
```

### 性能对比

```python
import time

models_to_test = ['ppocrv5_server', 'ppocrv5_mobile']

for model_id in models_to_test:
    start_time = time.time()
    result = ocr_engine.ocr(img, rec_model_id=model_id)
    end_time = time.time()
    
    print(f"模型 {model_id}:")
    print(f"  识别时间: {end_time - start_time:.3f}秒")
    print(f"  识别数量: {len(result[0])}个文本区域")
```

### 添加自定义模型

在YAML配置文件中添加新的模型配置：

```yaml
models:
  my_custom_model:
    model_name: "我的自定义模型"
    model_path: "/path/to/my/model.onnx"
    algorithm: "SVTR_LCNet"
    char_dict_path: "/path/to/my/dict.txt"
    image_shape: "3,48,320"
    batch_num: 6
    use_space_char: true
    max_text_length: 25
    description: "针对特定场景优化的模型"
    warmup_enabled: true
    optimization_level: "default"
    custom_params:
      use_fp16: false
      memory_optimization: true

  # 其他模型配置...
```

```python
ocr_engine = OCREngine(
    use_gpu=False,
    inference_engine='openvino'
)
```

## 配置参数说明

### OCREngine 参数

- `models_config_file`: YAML配置文件路径（默认: None，使用默认的models_config.yaml）

### 命令行参数

```bash
# 使用自定义配置文件
python your_script.py --models_config_file my_config.yaml
```

### 使用start.sh脚本

项目提供了`start.sh`脚本来启动OCR服务，支持通过命令行指定配置文件：

```bash
# 使用默认配置文件启动
./start.sh

# 使用自定义配置文件启动
./start.sh --models_config_file my_config.yaml

# 查看所有可用选项
./start.sh --help
```

## YAML配置文件详解

### 模型配置参数

每个模型支持以下配置参数：

- `model_name`: 模型显示名称
- `model_path`: 模型文件路径（支持相对路径）
- `algorithm`: 识别算法类型
- `char_dict_path`: 字符字典路径（支持相对路径）
- `image_shape`: 图像输入尺寸，格式："C,H,W"
- `batch_num`: 批处理数量
- `use_space_char`: 是否使用空格字符
- `max_text_length`: 最大文本长度
- `description`: 模型描述
- `image_inverse`: 是否图像反色
- `warmup_enabled`: 是否启用预热
- `warmup_iterations`: 预热迭代次数
- `optimization_level`: 优化级别（accurate/fast/default）
- `custom_params`: 自定义参数字典

### 全局配置参数

- `default_model_id`: 默认使用的模型ID
- `drop_score`: 识别结果过滤阈值
- `use_angle_cls`: 是否使用角度分类器
- `use_ori_cls`: 是否使用方向分类器
- `inference_engine`: 推理引擎类型
- `use_gpu`: 是否使用GPU
- 其他检测、分类相关参数...

## 性能建议

1. **高精度场景**: 使用 `ppocrv5_server`
2. **实时处理**: 使用 `ppocrv5_mobile`
3. **快速预览**: 使用 `ppocrv4`
4. **批量处理**: 根据图像大小和精度要求动态选择模型

## ⚠️ 重要注意事项

1. **YAML配置文件必须存在**：系统**严格依赖**YAML配置文件，如果`models_config.yaml`文件不存在，系统将直接抛出错误
2. **必要配置参数**：以下参数在YAML配置中是**必须的**，缺少任何一个都会导致系统启动失败：
   - `global_config.default_model_id`：默认模型ID
   - `global_config.drop_score`：识别结果过滤阈值
   - `global_config.use_angle_cls`：是否使用角度分类器
   - `global_config.use_ori_cls`：是否使用方向分类器
3. **无fallback机制**：系统不提供任何后备配置，所有参数都必须在YAML文件中明确指定
4. **路径配置**：模型文件和字典文件路径支持相对路径（相对于YAML文件位置）和绝对路径
5. **内存使用**：加载多个模型会占用更多内存，建议根据实际需求选择要加载的模型
6. **初始化开销**：首次加载模型时会有初始化开销，建议在应用启动时完成模型加载
7. **性能差异**：不同模型的识别精度和速度差异较大，请根据实际测试结果选择合适的模型
8. **严格验证**：系统会严格验证YAML配置的完整性，如果配置有误会抛出详细的错误信息

## 迁移指南

如果您之前使用的是硬编码配置，现在需要：

1. 创建或修改`models_config.yaml`文件
2. 将原有的模型配置参数移到YAML文件中
3. 移除代码中的硬编码参数
4. 使用新的YAML配置驱动的API
5. **注意**: `infer_args`函数已被删除，所有配置现在都来源于YAML文件

## 错误处理

系统会在以下情况下抛出详细的错误信息：

1. **配置文件不存在**：
   ```
   RuntimeError: YAML配置文件不存在: models_config.yaml
   ```

2. **缺少必要的全局配置参数**：
   ```
   RuntimeError: YAML配置文件中缺少 'drop_score' 参数
   RuntimeError: YAML配置文件中缺少 'use_angle_cls' 参数
   ```

3. **没有模型配置**：
   ```
   RuntimeError: YAML配置文件中没有找到任何模型配置，请检查models_config.yaml文件
   ```

4. **缺少默认模型ID**：
   ```
   RuntimeError: YAML配置文件中缺少 'default_model_id' 参数，请在global_config中设置
   ```

## 示例代码

完整的使用示例请参考：
- `test_no_infer_args.py` - 删除infer_args函数后的功能测试
- `test_pure_yaml_only.py` - 纯YAML配置功能测试
- `test_strict_yaml_config.py` - 严格YAML配置功能测试
- `models_config.yaml` - 完整的配置文件示例
- `start.sh` - 启动脚本，支持指定配置文件
