# CPU线程优化配置指南

## 问题描述

在32核CPU服务器上使用OpenVINO进行推理时，发现只有一半的核心被利用，而使用ONNX Runtime时能够充分利用全部32个核心。

## 解决方案

我们已经优化了推理引擎的CPU线程配置，现在支持以下方式来控制CPU核心的使用：

### 1. 命令行参数配置

使用 `--cpu_threads` 参数指定CPU线程数：

```bash
# 使用32个线程运行OpenVINO
./start.sh --cpu_threads 32

# 使用32个线程运行ONNX
./start.sh --inference_engine onnx --cpu_threads 32

# 使用16个线程运行（如果你想限制资源使用）
./start.sh --cpu_threads 16
```

### 2. 环境变量配置

设置环境变量来控制线程数：

```bash
# 对于OpenVINO
export OPENVINO_CPU_THREADS=32
./start.sh

# 对于ONNX Runtime
export ONNX_CPU_THREADS=32
./start.sh --inference_engine onnx
```

### 3. 默认行为

如果不指定任何配置，系统会自动检测并使用所有可用的CPU核心。

## 配置优化详情

### OpenVINO 配置优化

- **inference_num_threads**: 设置推理线程数
- **enable_cpu_pinning**: 启用CPU绑定以获得更好性能
- **scheduling_core_type**: 优先使用性能核心
- **enable_hyper_threading**: 启用超线程技术
- **execution_mode**: 设置为性能优化模式

### ONNX Runtime 配置优化

- **intra_op_num_threads**: 设置单个操作内的并行度
- **inter_op_num_threads**: 设置操作间的并行度（通常设为1）
- **execution_mode**: 设置为并行执行模式
- **graph_optimization_level**: 启用所有图优化

## 性能测试建议

1. **基准测试**：
   ```bash
   # 测试OpenVINO默认配置
   ./start.sh
   
   # 测试OpenVINO 32线程配置
   ./start.sh --cpu_threads 32
   
   # 测试ONNX 32线程配置
   ./start.sh --inference_engine onnx --cpu_threads 32
   ```

2. **监控CPU使用率**：
   ```bash
   # 使用htop或top监控CPU使用情况
   htop
   
   # 或者使用专门的CPU监控工具
   watch -n 1 'cat /proc/loadavg'
   ```

## 注意事项

1. **线程数设置**：通常设置为CPU核心数即可，过多的线程可能导致上下文切换开销增加
2. **内存考虑**：更多线程会消耗更多内存，请确保系统有足够的内存
3. **其他应用影响**：如果服务器还运行其他应用，建议适当减少线程数以避免资源竞争

## 故障排除

如果配置后仍然无法充分利用CPU核心：

1. 检查系统限制：
   ```bash
   ulimit -a
   ```

2. 检查CPU亲和性设置：
   ```bash
   taskset -c 0-31 ./start.sh --cpu_threads 32
   ```

3. 检查OpenVINO版本兼容性，建议使用最新版本

4. 监控实际的线程创建情况：
   ```bash
   ps -eLf | grep python | wc -l
   ```

## 总结

通过这些配置优化，OpenVINO现在应该能够充分利用所有32个CPU核心，性能应该与ONNX Runtime相当。建议根据实际的工作负载和系统资源情况调整线程数配置。
