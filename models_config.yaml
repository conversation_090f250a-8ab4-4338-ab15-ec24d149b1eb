# 全局配置
global_config:
  default_model_id: "ppocrv5_mobile"  # 默认使用的模型

  # 推理引擎配置
  inference_engine: "openvino"  # onnx, openvino
  use_gpu: false

  # 通用识别参数
  drop_score: 0.5

  # 检测相关参数
  det_algorithm: "DB"
  det_model_dir: "inference/models/ppocrv5/det/det.onnx"
  det_limit_side_len: 1500
  det_limit_type: "max"
  det_box_type: "quad"
  det_db_thresh: 0.3
  det_db_box_thresh: 0.6
  det_db_unclip_ratio: 2.3
  det_db_score_mode: "fast"
  use_dilation: false
  det_east_score_thresh: 0.8
  det_east_cover_thresh: 0.1
  det_east_nms_thresh: 0.2
  det_sast_score_thresh: 0.5
  det_sast_nms_thresh: 0.2
  det_pse_thresh: 0
  det_pse_box_thresh: 0.85
  det_pse_min_area: 16
  det_pse_scale: 1
  det_fcenet_score_thresh: 0.3

  # 分类相关参数
  use_angle_cls: false
  cls_model_dir: "inference/models/ppocrv4/cls/cls.onnx"
  cls_image_shape: "3,48,192"
  cls_batch_num: 6
  cls_thresh: 0.9
  label_list: [ "0", "180" ]

  # 方向分类相关参数
  use_ori_cls: true
  ori_model_dir: "inference/models/ppstructurev3/doc_img_cls.onnx"
  ori_image_shape: "3,224,224"
  ori_input_size: [ 224, 224 ]
  ori_batch_num: 6
  ori_thresh: 0.9

# 多模型配置文件
# 配置了多少个model_id，就加载多少个模型
models:
  ppocrv5_server:
    model_name: "PPOCRv5 服务器版"
    model_path: "inference/models/ppocrv5/rec/rec_server.onnx"
    algorithm: "SVTR_LCNet"
    char_dict_path: "inference/models/ppocrv5/ppocrv5_dict.txt"
    image_shape: "3,48,320"
    batch_num: 6
    use_space_char: true
    max_text_length: 25
    description: "PPOCRv5 服务器版本，精度较高"
    image_inverse: false

  ppocrv5_mobile:
    model_name: "PPOCRv5 轻量版"
    model_path: "inference/models/ppocrv5/rec/rec.onnx"
    algorithm: "SVTR_LCNet"
    char_dict_path: "inference/models/ppocrv5/ppocrv5_dict.txt"
    image_shape: "3,48,320"
    batch_num: 6
    use_space_char: true
    max_text_length: 25
    description: "PPOCRv5 轻量版本，速度较快"
    image_inverse: false
    
    # 高级配置
    warmup_enabled: true
    warmup_iterations: 3
    optimization_level: "fast"
    
    # 自定义参数
    custom_params:
      use_fp16: true
      memory_optimization: false

  ppocrv4:
    model_name: "PPOCRv4"
    model_path: "inference/models/ppocrv4/rec/rec.onnx"
    algorithm: "SVTR_LCNet"
    char_dict_path: "inference/models/ch_ppocr_server_v2.0/ppocr_keys_v1.txt"  # 使用v5的字典
    image_shape: "3,48,320"
    batch_num: 6
    use_space_char: true
    max_text_length: 25
    description: "PPOCRv4 版本"
    image_inverse: false
    
    # 高级配置
    warmup_enabled: true
    warmup_iterations: 3
    optimization_level: "default"
    
    # 自定义参数
    custom_params:
      use_fp16: false
      memory_optimization: true
