#!/usr/bin/env python3
"""
PPOCRv5 参数量快速统计工具
"""

import onnx
import numpy as np
from pathlib import Path
import os

def count_onnx_params(model_path):
    """统计ONNX模型参数量"""
    try:
        model = onnx.load(model_path)
        total_params = 0
        
        # 计算实际的参数量
        for init in model.graph.initializer:
            if init.dims:
                # 正确计算参数数量
                param_count = np.prod(init.dims)
                total_params += param_count
                
        # 如果没有找到参数，使用文件大小估算
        if total_params == 0:
            size_bytes = os.path.getsize(model_path)
            # 假设模型文件80%为参数，每个参数4字节(float32)
            total_params = int((size_bytes * 0.8) / 4)
            
        return total_params
    except Exception as e:
        # 备选方案：根据文件大小估算
        size_bytes = os.path.getsize(model_path)
        return int((size_bytes * 0.8) / 4)

def format_params(num):
    """格式化参数数量"""
    if num >= 1e6:
        return f"{num/1e6:.1f}M"
    elif num >= 1e3:
        return f"{num/1e3:.1f}K"
    else:
        return str(num)

def main():
    """主函数"""
    base_path = Path("inference/models/ppocrv5")
    
    models = {
        "det.onnx": base_path / "det" / "det.onnx",
        "det_server.onnx": base_path / "det" / "det_server.onnx", 
        "rec.onnx": base_path / "rec" / "rec.onnx",
        "rec_server.onnx": base_path / "rec" / "rec_server.onnx",
        "cls.onnx": base_path / "cls" / "cls.onnx"
    }
    
    print("PPOCRv5 模型参数量统计")
    print("=" * 40)
    
    for name, path in models.items():
        if path.exists():
            params = count_onnx_params(path)
            size_mb = os.path.getsize(path) / (1024**2)
            print(f"{name:20} {format_params(params):>8} ({size_mb:.1f}MB)")
    
    print("\n配置对比:")
    print("-" * 40)
    
    # Mobile配置
    mobile_params = 0
    mobile_size = 0
    for name in ["det.onnx", "rec.onnx", "cls.onnx"]:
        path = models[name]
        if path.exists():
            mobile_params += count_onnx_params(path)
            mobile_size += os.path.getsize(path) / (1024**2)
    
    # Server配置
    server_params = 0
    server_size = 0
    for name in ["det_server.onnx", "rec_server.onnx", "cls.onnx"]:
        path = models[name]
        if path.exists():
            server_params += count_onnx_params(path)
            server_size += os.path.getsize(path) / (1024**2)
    
    print(f"Mobile版本:          {format_params(mobile_params):>8} ({mobile_size:.1f}MB)")
    print(f"Server版本:          {format_params(server_params):>8} ({server_size:.1f}MB)")
    
    if mobile_params > 0 and server_params > 0:
        ratio = server_params / mobile_params
        print(f"Server版本参数量是Mobile版本的 {ratio:.1f} 倍")

if __name__ == "__main__":
    main() 