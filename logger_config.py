from pathlib import Path
import logging.config

def setup_logger(app_name: str = "ocr-server", log_dir: str = "logs"):
    # 创建日志目录
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    # 日志配置字典
    logging_config = {
        'version': 1,
        'disable_existing_loggers': False,  # 关键设置
        'formatters': {
            'standard': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
        },
        'filters': {
            'debug_only': {
                '()': 'logger_config.DebugFilter',
            },
            'info_only': {
                '()': 'logger_config.InfoFilter',
            },
            'error_only': {
                '()': 'logger_config.ErrorFilter',
            },
        },
        'handlers': {
            'debug_handler': {
                'class': 'logging.handlers.TimedRotatingFileHandler',
                'filename': f'{log_dir}/{app_name}-debug.log',
                'when': 'midnight',
                'interval': 1,
                'backupCount': 7,
                'formatter': 'standard',
                'encoding': 'utf-8',
                'level': 'DEBUG',
                'filters': ['debug_only'],
            },
            'info_handler': {
                'class': 'logging.handlers.TimedRotatingFileHandler',
                'filename': f'{log_dir}/{app_name}-info.log',
                'when': 'midnight',
                'interval': 1,
                'backupCount': 7,
                'formatter': 'standard',
                'encoding': 'utf-8',
                'level': 'INFO',
                'filters': ['info_only'],
            },
            'error_handler': {
                'class': 'logging.handlers.TimedRotatingFileHandler',
                'filename': f'{log_dir}/{app_name}-error.log',
                'when': 'midnight',
                'interval': 1,
                'backupCount': 7,
                'formatter': 'standard',
                'encoding': 'utf-8',
                'level': 'ERROR'
            }
        },
        'loggers': {
            '': {  # root logger
                'handlers': ['debug_handler', 'info_handler', 'error_handler'],
                'level': 'DEBUG',
                'propagate': False
            },
            app_name: {
                'handlers': ['debug_handler', 'info_handler', 'error_handler'],
                'level': 'DEBUG',
                'propagate': False
            }
        }
    }
    
    # 应用配置
    logging.config.dictConfig(logging_config)
    
    return logging.getLogger(app_name)


class DebugFilter(logging.Filter):
    """只允许 DEBUG 级别的日志通过"""
    def filter(self, record):
        return record.levelno == logging.DEBUG


class InfoFilter(logging.Filter):
    """只允许 INFO 级别的日志通过"""
    def filter(self, record):
        return record.levelno == logging.INFO


class ErrorFilter(logging.Filter):
    """只允许 ERROR 级别及以上的日志通过"""
    def filter(self, record):
        return record.levelno >= logging.ERROR
