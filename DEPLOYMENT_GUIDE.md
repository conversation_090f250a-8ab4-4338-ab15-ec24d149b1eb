# OCR服务器打包部署说明

## 环境要求

- **Python 3.11+**
- **pip/pip3** - 用于下载依赖包（注意：依赖包与操作系统绑定，Linux、Windows、macOS之间不能共用，测试环境OS需要与目标生产环境一致）
- **tar** - 用于解压缩（Linux/macOS自带）
- **PyArmor** - 可选，用于代码混淆保护

## 打包步骤

### 1. 执行打包脚本

```bash
python package_project.py
```

### 2. 选择混淆方式

当系统未安装PyArmor时，脚本会弹出选择提示：

```
选择操作:
  1. 自动安装PyArmor并继续
  2. 跳过混淆，直接打包
  3. 取消打包

请选择 [1/2/3]:
```

**建议**：
- **生产环境**：选择 `1` 自动安装PyArmor进行代码混淆
- **测试环境**：选择 `2` 跳过混淆，快速打包

### 3. 获取打包结果

打包完成后将生成：

```
ocr_server_1_0_0_build/          # 构建目录
ocr_server_1_0_0.tar.gz          # 部署压缩包
```

## 部署步骤

### 1. 解压部署包

```bash
tar -xzf ocr_server_1_0_0.tar.gz
cd ocr_server_1_0_0_build
```

### 2. 安装依赖

```bash
chmod +x install.sh
./install.sh
```

### 3. 启动服务

```bash
# 后台运行（默认）
./start.sh

# 前台运行（可查看实时日志）
./start.sh -fg

# 查看帮助（了解可以配置的参数）
./start.sh -h
```

### 4. 停止服务

```bash
./stop.sh
```

## 注意事项

- 确保目标服务器与打包环境为同一操作系统平台
- 首次部署时请使用前台模式启动（`./start.sh -fg`），确认服务正常运行
- 生产环境建议使用代码混淆保护源码安全 