"""
多模型文本识别器
支持同时加载多个识别模型，并根据参数选择使用哪个模型
"""
import cv2
import numpy as np
import math
from PIL import Image
from typing import Dict, List, Optional, Union, Tuple
import logging

from .rec_postprocess import CTCLabelDecode
from .engine_factory import create_inference_engine
from .model_config import RecModelConfig


class ModelInstance:
    """单个模型实例"""

    def __init__(self, config: RecModelConfig, inference_engine: str = "onnx", use_gpu: bool = False):
        self.config = config
        self.inference_engine_type = inference_engine
        self.use_gpu = use_gpu

        # 解析图像尺寸
        self.rec_image_shape = [int(v) for v in config.image_shape.split(",")]

        # 初始化后处理
        self.postprocess_op = CTCLabelDecode(
            character_dict_path=config.char_dict_path,
            use_space_char=config.use_space_char,
        )

        # 创建推理引擎
        self.inference_engine = create_inference_engine(
            model_path=config.model_path,
            inference_engine=inference_engine,
            use_gpu=use_gpu
        )

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"已加载模型: {config.model_name} ({config.model_id})")

    def resize_norm_img(self, img, max_wh_ratio):
        """图像预处理"""
        imgC, imgH, imgW = self.rec_image_shape
        algorithm = self.config.algorithm

        if algorithm == "NRTR" or algorithm == "ViTSTR":
            img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            image_pil = Image.fromarray(np.uint8(img))
            if algorithm == "ViTSTR":
                img = image_pil.resize([imgW, imgH], Image.BICUBIC)
            else:
                img = image_pil.resize([imgW, imgH], Image.ANTIALIAS)
            img = np.array(img)
            norm_img = np.expand_dims(img, -1)
            norm_img = norm_img.transpose((2, 0, 1))
            if algorithm == "ViTSTR":
                norm_img = norm_img.astype(np.float32) / 255.0
            else:
                norm_img = norm_img.astype(np.float32) / 128.0 - 1.0
            return norm_img
        elif algorithm == "RFL":
            img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            image_pil = Image.fromarray(np.uint8(img))
            img = image_pil.resize([imgW, imgH], Image.ANTIALIAS)
            img = np.array(img)
            norm_img = np.expand_dims(img, -1)
            norm_img = norm_img.transpose((2, 0, 1))
            norm_img = norm_img.astype(np.float32) / 255.0
            return norm_img

        assert imgC == img.shape[2]
        imgW = int((imgH * max_wh_ratio))
        if self.use_gpu:
            imgW = min(imgW, 2560)
        else:
            imgW = min(imgW, 1536)

        h, w = img.shape[:2]
        ratio = w / float(h)
        if math.ceil(imgH * ratio) > imgW:
            resized_w = imgW
        else:
            resized_w = int(math.ceil(imgH * ratio))

        resized_image = cv2.resize(img, (resized_w, imgH))
        resized_image = resized_image.astype('float32')
        resized_image = resized_image.transpose((2, 0, 1)) / 255
        resized_image -= 0.5
        resized_image /= 0.5
        padding_im = np.zeros((imgC, imgH, imgW), dtype=np.float32)
        padding_im[:, :, 0:resized_w] = resized_image
        return padding_im

    def predict(self, img_list: List[np.ndarray]) -> List[Tuple[str, float]]:
        """预测文本"""
        img_num = len(img_list)
        width_list = []
        for img in img_list:
            width_list.append(img.shape[1] / float(img.shape[0]))

        indices = np.argsort(np.array(width_list))
        rec_res = [["", 0.0]] * img_num
        batch_num = self.config.batch_num

        for beg_img_no in range(0, img_num, batch_num):
            end_img_no = min(img_num, beg_img_no + batch_num)
            norm_img_batch = []
            imgC, imgH, imgW = self.rec_image_shape
            max_wh_ratio = imgW / imgH

            for ino in range(beg_img_no, end_img_no):
                h, w = img_list[indices[ino]].shape[0:2]
                wh_ratio = w * 1.0 / h
                max_wh_ratio = max(max_wh_ratio, wh_ratio)

            for ino in range(beg_img_no, end_img_no):
                norm_img = self.resize_norm_img(img_list[indices[ino]], max_wh_ratio)
                norm_img = norm_img[np.newaxis, :]
                norm_img_batch.append(norm_img)

            norm_img_batch = np.concatenate(norm_img_batch)
            norm_img_batch = norm_img_batch.copy()

            # 推理
            outputs = self.inference_engine.predict(norm_img_batch)
            preds = outputs[0]

            # 后处理
            rec_result = self.postprocess_op(preds)
            for rno in range(len(rec_result)):
                rec_res[indices[beg_img_no + rno]] = rec_result[rno]

        return rec_res


class MultiModelTextRecognizer:
    """多模型文本识别器"""

    def __init__(self,
                 model_ids: List[str],
                 inference_engine: str = "onnx",
                 use_gpu: bool = False,
                 default_model_id: str = None):
        """
        初始化多模型识别器

        Args:
            model_ids: 要加载的模型ID列表
            inference_engine: 推理引擎类型
            use_gpu: 是否使用GPU
            default_model_id: 默认使用的模型ID
            config_manager: 配置管理器实例，如果为None则使用全局实例
        """
        self.inference_engine_type = inference_engine
        self.use_gpu = use_gpu
        self.models: Dict[str, ModelInstance] = {}
        self.logger = logging.getLogger(__name__)

        # 获取ModelConfigManager单例实例
        from .model_config import ModelConfigManager
        self.config_manager = ModelConfigManager.get_instance()

        # 加载指定的模型
        for model_id in model_ids:
            config = self.config_manager.get_config(model_id)
            if config is None:
                self.logger.warning(f"模型配置不存在: {model_id}")
                continue

            try:
                model_instance = ModelInstance(config, inference_engine, use_gpu)
                self.models[model_id] = model_instance
                self.logger.info(f"成功加载模型: {model_id}")
            except Exception as e:
                self.logger.error(f"加载模型失败 {model_id}: {e}")

        # 设置默认模型
        if default_model_id and default_model_id in self.models:
            self.default_model_id = default_model_id
        elif self.models:
            self.default_model_id = list(self.models.keys())[0]
        else:
            raise RuntimeError("没有成功加载任何模型")

        self.logger.info(f"默认模型: {self.default_model_id}")
        self.logger.info(f"已加载 {len(self.models)} 个模型: {list(self.models.keys())}")

    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        return list(self.models.keys())

    def get_model_info(self, model_id: str) -> Optional[Dict]:
        """获取模型信息"""
        if model_id in self.models:
            config = self.models[model_id].config
            return {
                "model_id": config.model_id,
                "model_name": config.model_name,
                "algorithm": config.algorithm,
                "image_shape": config.image_shape,
                "description": config.description
            }
        return None

    def predict(self,
                img_list: List[np.ndarray],
                model_id: Optional[str] = None) -> List[Tuple[str, float]]:
        """
        预测文本
        
        Args:
            img_list: 图像列表
            model_id: 指定使用的模型ID，如果为None则使用默认模型
            
        Returns:
            识别结果列表
        """
        # 确定使用的模型
        if model_id is None:
            model_id = self.default_model_id

        if model_id not in self.models:
            raise ValueError(f"模型不存在: {model_id}")

        model_instance = self.models[model_id]
        return model_instance.predict(img_list)

    def __call__(self, img_list: List[np.ndarray], model_id: Optional[str] = None) -> List[Tuple[str, float]]:
        """调用接口，兼容原有接口"""
        return self.predict(img_list, model_id)
