import argparse
from typing import Optional, List, Dict, Any

from .predict_system import TextSystem
from .model_config import ModelConfigManager


class OCREngine(TextSystem):

    def __init__(self, **kwargs):
        # 获取ModelConfigManager单例实例
        config_manager = ModelConfigManager.get_instance()

        # 使用新的get方法创建参数命名空间
        params = config_manager.get(**kwargs)

        # 初始化模型
        super().__init__(params)

    def ocr(self, img, det=True, rec=True, rec_model_id: Optional[str] = None):
        """
        OCR识别

        Args:
            img: 输入图像
            det: 是否进行文字检测
            rec: 是否进行文字识别
            cls: 是否进行方向分类
            rec_model_id: 指定使用的识别模型ID（仅在多模型模式下有效）

        Returns:
            OCR结果
        """
        if det and rec:
            ocr_res = []
            dt_boxes, rec_res = self.__call__(img, rec_model_id)
            tmp_res = [[box.tolist(), res] for box, res in zip(dt_boxes, rec_res)]
            ocr_res.append(tmp_res)
            return ocr_res
        elif det and not rec:
            ocr_res = []
            dt_boxes = self.text_detector(img)
            tmp_res = [box.tolist() for box in dt_boxes]
            ocr_res.append(tmp_res)
            return ocr_res
        else:
            ocr_res = []
            if not isinstance(img, list):
                img = [img]
            rec_res = self.text_recognizer(img)
            ocr_res.append(rec_res)
            return ocr_res

    def get_available_rec_models(self) -> List[str]:
        """获取可用的识别模型列表"""
        return super().get_available_rec_models()

    def get_rec_model_info(self, model_id: str) -> Optional[Dict[str, Any]]:
        """获取识别模型信息"""
        return super().get_rec_model_info(model_id)

    def list_rec_models(self) -> Dict[str, Dict[str, Any]]:
        """列出所有可用的识别模型信息"""
        models = self.get_available_rec_models()
        return {model_id: self.get_rec_model_info(model_id) for model_id in models}
