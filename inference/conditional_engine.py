from typing import Dict, List, Type, Callable, Any
import functools
import logging
from .engine_interface import InferenceEngine


class EngineRegistry:
    """推理引擎注册系统"""
    
    _engines: Dict[str, Type[InferenceEngine]] = {}
    
    @classmethod
    def register_engine(cls, engine_name: str, engine_class: Type[InferenceEngine]):
        """
        直接注册引擎类
        
        Args:
            engine_name: 引擎名称（如'onnx', 'openvino'）
            engine_class: 引擎类
        """
        if not issubclass(engine_class, InferenceEngine):
            raise TypeError(f"{engine_class.__name__} must inherit from InferenceEngine")
        
        cls._engines[engine_name] = engine_class
        # 改为DEBUG级别，避免重复日志
        logging.getLogger(__name__).debug(f"注册引擎: {engine_name} -> {engine_class.__name__}")
    
    @classmethod
    def get_engine_class(cls, engine_name: str) -> Type[InferenceEngine]:
        """根据引擎名称获取引擎类"""
        if engine_name not in cls._engines:
            raise ValueError(f"Engine '{engine_name}' not found. Available engines: {list(cls._engines.keys())}")
        
        return cls._engines[engine_name]
    
    @classmethod
    def list_engines(cls) -> Dict[str, str]:
        """列出所有注册的引擎"""
        return {name: engine_class.__name__ for name, engine_class in cls._engines.items()}





