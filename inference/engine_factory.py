from typing import Dict, Any, Optional, Type, Union
import logging
from .engine_interface import InferenceEngine
from .conditional_engine import EngineRegistry
from .engine_implementations import *  # 导入所有引擎实现，触发装饰器注册


class EngineFactory:
    """推理引擎工厂类"""

    _instance = None
    _engines: Dict[str, InferenceEngine] = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def create_engine(self,
                      model_path: str,
                      inference_engine: str = "onnx",
                      use_gpu: bool = False,
                      **kwargs) -> InferenceEngine:
        """
        根据引擎名称创建推理引擎
        
        Args:
            model_path: 模型文件路径
            inference_engine: 推理引擎名称（'onnx', 'openvino'等）
            use_gpu: 是否使用GPU
            **kwargs: 其他参数
            
        Returns:
            InferenceEngine: 推理引擎实例
        """
        # 构建引擎标识
        engine_key = self._build_engine_key(model_path, inference_engine, use_gpu)

        # 检查是否已存在
        if engine_key in self._engines:
            self.logger.debug(f"重用已存在的推理引擎: {engine_key}")
            return self._engines[engine_key]

        try:
            # 获取引擎类
            engine_class = EngineRegistry.get_engine_class(inference_engine)

            # 创建引擎实例
            engine = engine_class(model_path, use_gpu, **kwargs)

            # 初始化引擎
            engine.initialize()

            # 缓存引擎实例
            self._engines[engine_key] = engine

            # 改为DEBUG级别，避免重复日志
            self.logger.debug(f"创建推理引擎: {engine}")

            return engine

        except Exception as e:
            self.logger.error(f"创建推理引擎失败: {e}")
            raise

    def get_engine(self,
                   model_path: str,
                   inference_engine: str = "onnx",
                   use_gpu: bool = False) -> Optional[InferenceEngine]:
        """
        获取现有的推理引擎实例
        
        Args:
            model_path: 模型文件路径
            inference_engine: 推理引擎名称（'onnx', 'openvino'等）
            use_gpu: 是否使用GPU
            
        Returns:
            Optional[InferenceEngine]: 推理引擎实例或None
        """
        engine_key = self._build_engine_key(model_path, inference_engine, use_gpu)
        return self._engines.get(engine_key)

    def remove_engine(self,
                      model_path: str,
                      inference_engine: str = "onnx",
                      use_gpu: bool = False) -> bool:
        """
        移除推理引擎实例
        
        Args:
            model_path: 模型文件路径
            inference_engine: 推理引擎名称（'onnx', 'openvino'等）
            use_gpu: 是否使用GPU
            
        Returns:
            bool: 是否成功移除
        """
        engine_key = self._build_engine_key(model_path, inference_engine, use_gpu)
        if engine_key in self._engines:
            del self._engines[engine_key]
            self.logger.info(f"移除推理引擎: {engine_key}")
            return True
        return False

    def clear_engines(self) -> None:
        """清空所有引擎实例"""
        self._engines.clear()
        self.logger.info("清空所有推理引擎")

    def list_engines(self) -> Dict[str, str]:
        """列出所有已创建的引擎"""
        return {key: str(engine) for key, engine in self._engines.items()}

    def _build_engine_key(self,
                          model_path: str,
                          inference_engine: str,
                          use_gpu: bool) -> str:
        """构建引擎标识键"""
        return f"{model_path}|engine={inference_engine}|gpu={use_gpu}"

    @staticmethod
    def get_available_engines() -> Dict[str, str]:
        """获取所有可用的引擎类型"""
        return EngineRegistry.list_engines()


# 创建全局工厂实例
engine_factory = EngineFactory()


def create_inference_engine(model_path: str,
                            inference_engine: str = "onnx",
                            use_gpu: bool = False,
                            **kwargs) -> InferenceEngine:
    """
    便利函数：创建推理引擎
    
    Args:
        model_path: 模型文件路径
        inference_engine: 推理引擎名称（'onnx', 'openvino'等）
        use_gpu: 是否使用GPU
        **kwargs: 其他参数
        
    Returns:
        InferenceEngine: 推理引擎实例
    """
    return engine_factory.create_engine(
        model_path=model_path,
        inference_engine=inference_engine,
        use_gpu=use_gpu,
        **kwargs
    )


def register_engine(engine_name: str, engine_class: Type[InferenceEngine]):
    """
    便利函数：直接注册引擎类
    
    Args:
        engine_name: 引擎名称
        engine_class: 引擎类
    """
    EngineRegistry.register_engine(engine_name, engine_class)
