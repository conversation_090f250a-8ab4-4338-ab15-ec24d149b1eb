from typing import Optional, List

from .multi_model_recognizer import MultiModelTextRecognizer


class TextRecognizer:
    def __init__(self, args):
        self.args = args

        # 从YAML配置中获取要加载的模型列表
        model_ids = args.model_ids

        # 从YAML配置中获取默认模型
        default_model_id = args.default_model_id

        self.multi_recognizer = MultiModelTextRecognizer(
            model_ids=model_ids,
            inference_engine=args.inference_engine,
            use_gpu=args.use_gpu,
            default_model_id=default_model_id
        )


    def __call__(self, img_list, model_id: Optional[str] = None):
        """
        文本识别

        Args:
            img_list: 图像列表
            model_id: 指定使用的模型ID

        Returns:
            识别结果列表
        """
        # 统一使用多模型识别器
        return self.multi_recognizer.predict(img_list, model_id)

    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        return self.multi_recognizer.get_available_models()

    def get_model_info(self, model_id: str) -> Optional[dict]:
        """获取模型信息"""
        return self.multi_recognizer.get_model_info(model_id)
