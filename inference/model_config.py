"""
多模型配置管理器
支持管理多个识别模型的配置信息
支持从YAML文件动态加载配置
"""
import os
import yaml
import argparse
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class RecModelConfig:
    """识别模型配置"""
    model_id: str  # 模型唯一标识
    model_name: str  # 模型显示名称
    model_path: str  # 模型文件路径
    algorithm: str  # 识别算法类型
    char_dict_path: str  # 字符字典路径
    image_shape: str  # 图像输入尺寸 "3,48,320"
    batch_num: int = 6  # 批处理数量
    use_space_char: bool = True  # 是否使用空格字符
    max_text_length: int = 25  # 最大文本长度
    description: str = ""  # 模型描述
    image_inverse: bool = False  # 是否图像反色

    # 高级配置
    warmup_enabled: bool = True  # 是否启用预热
    warmup_iterations: int = 3  # 预热迭代次数
    optimization_level: str = "default"  # 优化级别: accurate, fast, default

    # 自定义参数
    custom_params: Dict[str, Any] = field(default_factory=dict)


class ModelConfigManager:
    """模型配置管理器 - 单例模式"""

    _instance = None
    _initialized = False

    def __new__(cls, config_file: Optional[str] = None):
        if cls._instance is None:
            cls._instance = super(ModelConfigManager, cls).__new__(cls)
        return cls._instance

    def __init__(self, config_file: Optional[str] = None):
        # 避免重复初始化
        if self._initialized:
            return

        self._configs: Dict[str, RecModelConfig] = {}
        self._default_model_id: Optional[str] = None
        self._global_config: Dict[str, Any] = {}

        # 确定配置文件路径
        if config_file is None:
            # 默认配置文件路径
            project_root = Path(__file__).parent.parent
            config_file = project_root / "models_config.yaml"

        self.config_file = Path(config_file)
        self._load_configs()
        self._initialized = True

    @classmethod
    def get_instance(cls, config_file: Optional[str] = None) -> 'ModelConfigManager':
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls(config_file)
        return cls._instance

    def get(self, **kwargs) -> argparse.Namespace:
        """
        获取合并后的配置参数

        处理逻辑：
        1. 先读取yaml文件里的所有参数值
        2. 合并kwargs的参数值
        3. 如果有冲突则优先使用kwargs的参数值
        4. 返回argparse.Namespace对象

        Args:
            **kwargs: 额外的参数，会覆盖YAML配置中的同名参数

        Returns:
            argparse.Namespace: 合并后的参数对象
        """
        # 1. 从YAML配置获取所有参数
        config_params = self._global_config.copy()

        # 2. 添加模型相关的参数
        config_params['model_ids'] = self.list_models()
        config_params['default_model_id'] = self._default_model_id

        # 3. 合并kwargs参数，kwargs优先级更高
        config_params.update(kwargs)

        # 4. 创建并返回argparse.Namespace对象
        return argparse.Namespace(**config_params)

    def _load_configs(self):
        """从YAML文件加载配置"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)

                # 加载全局配置
                self._global_config = config_data.get('global_config', {})

                # 设置默认模型，必须在YAML配置中指定
                self._default_model_id = self._global_config.get('default_model_id')
                if self._default_model_id is None:
                    raise RuntimeError("YAML配置文件中缺少 'default_model_id' 参数，请在global_config中设置")

                # 加载模型配置
                models_config = config_data.get('models', {})
                project_root = self.config_file.parent

                for model_id, model_data in models_config.items():
                    # 处理相对路径
                    model_path = model_data['model_path']
                    if not os.path.isabs(model_path):
                        model_path = str(project_root / model_path)

                    char_dict_path = model_data['char_dict_path']
                    if not os.path.isabs(char_dict_path):
                        char_dict_path = str(project_root / char_dict_path)

                    # 创建模型配置
                    config = RecModelConfig(
                        model_id=model_id,
                        model_name=model_data['model_name'],
                        model_path=model_path,
                        algorithm=model_data['algorithm'],
                        char_dict_path=char_dict_path,
                        image_shape=model_data['image_shape'],
                        batch_num=model_data.get('batch_num', 6),
                        use_space_char=model_data.get('use_space_char', True),
                        max_text_length=model_data.get('max_text_length', 25),
                        description=model_data.get('description', ''),
                        image_inverse=model_data.get('image_inverse', False),
                        warmup_enabled=model_data.get('warmup_enabled', True),
                        warmup_iterations=model_data.get('warmup_iterations', 3),
                        optimization_level=model_data.get('optimization_level', 'default'),
                        custom_params=model_data.get('custom_params', {})
                    )

                    # 注册配置（跳过文件存在性检查，因为可能在不同环境中）
                    self._configs[model_id] = config

                print(f"从YAML文件加载了 {len(self._configs)} 个模型配置")

            except Exception as e:
                print(f"加载YAML配置文件失败: {e}")
                raise RuntimeError(f"无法加载YAML配置文件: {self.config_file}")
        else:
            print(f"配置文件不存在: {self.config_file}")
            raise RuntimeError(f"YAML配置文件不存在: {self.config_file}")


    
    def register_config(self, config: RecModelConfig, check_files: bool = True) -> None:
        """注册模型配置"""
        if check_files:
            if not os.path.exists(config.model_path):
                raise FileNotFoundError(f"模型文件不存在: {config.model_path}")

            if not os.path.exists(config.char_dict_path):
                raise FileNotFoundError(f"字典文件不存在: {config.char_dict_path}")

        self._configs[config.model_id] = config
    
    def get_config(self, model_id: str) -> Optional[RecModelConfig]:
        """获取模型配置"""
        return self._configs.get(model_id)
    
    def get_default_config(self) -> Optional[RecModelConfig]:
        """获取默认模型配置"""
        if self._default_model_id:
            return self._configs.get(self._default_model_id)
        return None
    
    def set_default_model(self, model_id: str) -> None:
        """设置默认模型"""
        if model_id not in self._configs:
            raise ValueError(f"模型ID不存在: {model_id}")
        self._default_model_id = model_id
    
    def list_models(self) -> List[str]:
        """列出所有可用的模型ID"""
        return list(self._configs.keys())
    
    def get_model_info(self, model_id: str) -> Dict[str, Any]:
        """获取模型信息"""
        config = self.get_config(model_id)
        if not config:
            return {}
        
        return {
            "model_id": config.model_id,
            "model_name": config.model_name,
            "algorithm": config.algorithm,
            "image_shape": config.image_shape,
            "description": config.description,
            "model_exists": os.path.exists(config.model_path),
            "dict_exists": os.path.exists(config.char_dict_path)
        }
    
    def get_all_models_info(self) -> Dict[str, Dict[str, Any]]:
        """获取所有模型信息"""
        return {model_id: self.get_model_info(model_id) for model_id in self._configs.keys()}

    def get_global_config(self) -> Dict[str, Any]:
        """获取全局配置"""
        return self._global_config.copy()

    def get_global_config_value(self, key: str, default=None):
        """获取全局配置中的特定值"""
        return self._global_config.get(key, default)
    
    def add_custom_config(self, 
                         model_id: str,
                         model_name: str,
                         model_path: str,
                         algorithm: str,
                         char_dict_path: str,
                         image_shape: str = "3,48,320",
                         **kwargs) -> None:
        """添加自定义模型配置"""
        config = RecModelConfig(
            model_id=model_id,
            model_name=model_name,
            model_path=model_path,
            algorithm=algorithm,
            char_dict_path=char_dict_path,
            image_shape=image_shape,
            **kwargs
        )
        self.register_config(config)

    @property
    def default_model_id(self):
        return self._default_model_id

