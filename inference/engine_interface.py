from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
import numpy as np


class InferenceEngine(ABC):
    """推理引擎抽象基类"""
    
    def __init__(self, model_path: str, use_gpu: bool = False, **kwargs):
        self.model_path = model_path
        self.use_gpu = use_gpu
        self.kwargs = kwargs
        self.model = None
        self.input_names = []
        self.output_names = []
        
    @abstractmethod
    def initialize(self) -> None:
        """初始化推理引擎"""
        pass
    
    @abstractmethod
    def predict(self, inputs: np.ndarray) -> List[np.ndarray]:
        """执行推理"""
        pass
    
    @abstractmethod
    def get_input_names(self) -> List[str]:
        """获取输入层名称"""
        pass
    
    @abstractmethod
    def get_output_names(self) -> List[str]:
        """获取输出层名称"""
        pass
    
    @property
    @abstractmethod
    def engine_type(self) -> str:
        """返回引擎类型"""
        pass
    
    def __str__(self) -> str:
        return f"{self.engine_type}Engine(model_path={self.model_path}, use_gpu={self.use_gpu})" 